using Microsoft.Practices.Unity;
using Siepe.GenevaUtility;
using Siepe.GenevaUtility.Entities;
using Siepe.Instruments.Data;
using Siepe.Instruments.Entities;
using Siepe.Service.BloombergRawMessage.Contract;
using Siepe.Shared.DBUtility;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public class FutureHandler : BaseInstrumentTypeHandler, IInstrumentTypeHandler
    {
        [Dependency]
        public ISqlDbAccess _sql { get; set; }

        [Dependency]
        public IInstrumentProvider _futuresProvider { get; set; }

        public override string GetQuantity(Trade trade)
        {
            Instrument inst = GetInstrument(trade.TradeDetails.UniqueBloombergID);
            Future future = GetFuture(inst.Id);
            decimal contractSize = 1;
            if (future != null && future.ContractSize > 0)
            {
                contractSize = future.ContractSize;
            }
            decimal quantity = ConvertDecimal(trade.TradeDetails.TradeAmount) / contractSize;
            return quantity.ToString();
        }

        protected Instrument GetInstrument(string bloombergId)
        {
            return _sql.GetFromXml<Instrument>("dbo.pInstByBloombergID", new[] { new SqlParameter("@BloombergUniqueID", bloombergId) });
        }

        protected Future GetFuture(long instId)
        {
            return (Future)_futuresProvider.Get(instId);
        }

        private decimal ConvertDecimal(string tradeAmt)
        {
            if (decimal.TryParse(tradeAmt, out decimal d))
            {
                return d;
            }
            return default(decimal);
        }
    }
}
