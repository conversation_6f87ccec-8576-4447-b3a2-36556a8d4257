using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Siepe.Allocations.Entities;
using Siepe.GenevaUtility.Entities;
using Siepe.Orders.Entities;
using OrderInstrument = Siepe.Orders.Entities.Instrument;
using Instrument = Siepe.Instruments.Entities.Instrument;
using Siepe.Shared.DBUtility;
using Siepe.Instruments.Data;
using AllocationPortfolio = Siepe.Allocations.Entities.Portfolio;
using Portfolio = Siepe.Service.BloombergRawMessage.Contract.Portfolio;
using Siepe.GenevaUtility;
using System.Data.SqlClient;
using Siepe.Service.BloombergRawMessage.Impl.TradeFeedProcessors;
using Siepe.PubSubUtility;
using Siepe.Serialization;
using System.Threading;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public class AllocationProvider : IAllocationProvider
    {
        private readonly IInstrumentDataProvider instrumentProvider;
        private readonly ISqlDbAccess sqlDbAccess;
        private readonly IGenevaDataProvider genevaDataProvider;
        private readonly IPublicationService publicationService;
        private readonly ISerializer serializer;

        public AllocationProvider(IInstrumentDataProvider instrumentDataProvider, ISqlDbAccess sqlDbAccess, IGenevaDataProvider genevaDataProvider,
            IPublicationService publicationService, ISerializer serializer)
        {
            this.instrumentProvider = instrumentDataProvider;
            this.sqlDbAccess = sqlDbAccess;
            this.genevaDataProvider = genevaDataProvider;
            this.publicationService = publicationService;
            this.serializer = serializer;
        }


        public Allocation BuildAllocation(RawTrade rawTrade)
        {
            var allocation = new Allocation();
            var inst = GetInstrumentFromBloombergID(rawTrade.BloombergID);
            var execution = new Execution()
            {
                LastQty = rawTrade.Quantity,
                LastPrice = rawTrade.Price,
                OrderSide = new OrderParameter() { Name = rawTrade.SideCode },
                Broker = new Broker() { Code = rawTrade.Broker }
            };

            if(inst != null)
            {
                execution.Instrument = new OrderInstrument() { Id = inst.Id, Name = inst.Name, Type = inst.InstrumentType?.Name };
            }

            allocation.PercentOfOrder = 1;
            allocation.Execution = execution;
            allocation.AllocationQty = execution.LastQty;
            //allocation.Portfolio = GetPortfolio(rawTrade.Portfolio, rawTrade.LocationAccount);
            return allocation;
        }

        private AllocationPortfolio GetPortfolio(string accountName, string locationAccount)
        {
            AllocationPortfolio portfolio = new AllocationPortfolio();
            try
            {
                GenevaLoader loader = genevaDataProvider.GetPortfolioXmlByBloombergID(accountName, locationAccount);
                string idStr = loader.PartyRecords.LocationAccount_InsertUpdate.UserDef1;
                long id = Convert.ToInt64(idStr);
                Portfolio corePortfolio = sqlDbAccess.GetFromXml<Portfolio>("Client.pPortfolio", new[] { new SqlParameter("@PortfolioID", id) });
                if (corePortfolio != null)
                {
                    portfolio.ID = corePortfolio.Id;
                    portfolio.Name = corePortfolio.Name;
                    portfolio.Custodian = corePortfolio.Custodian?.Name;
                }
            }
            catch (Exception e)
            {

            }
            return portfolio;
        }

        private Instrument GetInstrumentFromBloombergID(string bloombergID)
        {
            return instrumentProvider.GetInstrumentByBloombergID(bloombergID);
        }

        public long SaveBlock(RawTrade rawTrade)
        {
            var xmlHelper = new XmlHelper<RawTrade>();
            string xml = xmlHelper.Serialize(rawTrade);
            var id = sqlDbAccess.ExecuteScalar("Allocations.pSaveBlockRawMessage", new[] { new SqlParameter("@DailyExecutionXml", xml) });

            sqlDbAccess.ExecuteNonQuery("Allocations.pAllocationAuditEventI", new[] {
                new SqlParameter("@EventName", "Block Create"),
                new SqlParameter("@PKID", rawTrade.UserTranId1),
                new SqlParameter("@EventDetails", serializer.Serialize(rawTrade))
                });
            return Convert.ToInt64(id);
        }

        public void SaveAllocation(RawTrade rawTrade)
        {
            var xmlHelper = new XmlHelper<RawTrade>();
            string xml = xmlHelper.Serialize(rawTrade);
            bool retry = false;
            try
            {
                sqlDbAccess.ExecuteNonQuery("Allocations.pSaveDefaultAllocationRawMessage", new[] { new SqlParameter("@DailyExecutionXml", xml) });
            }
            catch (SqlException ex)
            {
                if (ex.Message.Contains("Cannot insert the value NULL into column 'BlockExecutionMapID'"))
                {
                    retry = true;
                }
                else
                {
                    throw ex;
                }
            }

            if (retry)
            {
                //Give enough time for the block to save, and then retry a single time.
                Thread.Sleep(5000);
                sqlDbAccess.ExecuteNonQuery("Allocations.pSaveDefaultAllocationRawMessage", new[] { new SqlParameter("@DailyExecutionXml", xml) });
            }

            sqlDbAccess.ExecuteNonQuery("Allocations.pAllocationAuditEventI", new[] {
                new SqlParameter("@EventName", "Default Allocation Created"),
                new SqlParameter("@PKID", rawTrade.ParentExecutionID),
                new SqlParameter("@EventDetails", serializer.Serialize(rawTrade))
            });
        }

        public void CancelBlock(RawTrade rawTrade)
        {
            var blockTicketId = rawTrade.UserTranId1;
            var cancelled = new List<CancelledAllocation>();

            sqlDbAccess.ExecuteReader("Allocations.pCancelBlock", new[] { new SqlParameter("@BlockUserTranId", blockTicketId) }, withReaderAction: (reader, command, connection) =>
            {
                while (reader.Read())
                {
                    var cancel = new CancelledAllocation
                    {
                        Id = reader.Get<long>("Id"),
                        AccountingSystem = reader.Get<string>("AccountingSystem"),
                        IsCtm = reader.Get<bool>("IsCTM"),
                        TicketNumber = reader.Get<string>("TicketNumber"),
                        ParentTicket = reader.Get<string>("ParentTicket")
                    };
                    cancelled.Add(cancel);
                }
            });

            sqlDbAccess.ExecuteNonQuery("Allocations.pAllocationAuditEventI", new[] {
                new SqlParameter("@EventName", "Block Cancel"),
                new SqlParameter("@PKID", rawTrade.UserTranId1),
                });

            foreach (var cancel in cancelled)
            {
                if (cancel.AccountingSystem == "Advent Geneva")
                {
                    publicationService.PublishMessage(0, null, null, null, Encoding.ASCII.GetBytes(cancel.Id.ToString()), "Trade", "Raw", "Added");
                }
                else if (cancel.AccountingSystem == "WSO")
                {
                    publicationService.PublishMessage(0, null, null, null, Encoding.ASCII.GetBytes(cancel.Id.ToString()), "Trade", "WSORaw", "Added");
                }
                else if (cancel.AccountingSystem == "FIS VPM")
                {
                    publicationService.PublishMessage(0, null, null, null, Encoding.ASCII.GetBytes(cancel.Id.ToString()), "VPM", "Trade", "Cancel");
                }

                sqlDbAccess.ExecuteNonQuery("Allocations.pAllocationAuditEventI", new[] {
                    new SqlParameter("@EventName", "Send Acct System Cancel"),
                    new SqlParameter("@PKID", cancel.Id),
                });

                if (cancel.IsCtm)
                {
                    var allocationIdentifier = new AllocationIdentifier
                    {
                        AllocationReferenceNumber = cancel.TicketNumber,
                        BlockReferenceNumber = cancel.ParentTicket
                    };

                    var xmlHelper = new XmlHelper<AllocationIdentifier>();

                    sqlDbAccess.ExecuteNonQuery("Allocations.pAllocationAuditEventI", new[] {
                        new SqlParameter("@EventName", "Send CTM Cancel"),
                        new SqlParameter("@PKID", cancel.TicketNumber),
                    });
                    publicationService.PublishMessage(0, null, null, null, xmlHelper.SerializeToByteArray(allocationIdentifier), "CTMAdapter", "CancelTrade");
                }
            }
        }
    }
}
