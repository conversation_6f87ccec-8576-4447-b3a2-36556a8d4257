using Siepe.Allocations.Entities;
using Siepe.GenevaUtility.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public interface IAllocationProvider
    {
        Allocation BuildAllocation(RawTrade rawTrade);
        long SaveBlock(RawTrade rawTrade);
        void SaveAllocation(RawTrade rawTrade);
        void CancelBlock(RawTrade rawTrade);
    }
}
