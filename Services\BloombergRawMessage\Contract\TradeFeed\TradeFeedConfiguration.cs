using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Contract
{
    [DataContract]
    public class TradeFeedConfiguration
    {
        [DataMember]
        public List<TradeFeedConfigurationItem> FieldMappings { get; set; } = new List<TradeFeedConfigurationItem>();

        [DataMember]
        public List<TransactionTypeConfiguration> TransactionTypes { get; set; } = new List<TransactionTypeConfiguration>();

        [DataMember]
        public List<int> CreateRecordTypes { get; set; } = new List<int>();

        [DataMember]
        public List<int> CancelRecordTypes { get; set; } = new List<int>();

        [DataMember]
        public List<int> BlockCreateRecordTypes { get; set; } = new List<int>();

        [DataMember]
        public List<int> BlockCancelRecordTypes { get; set; } = new List<int>();

        [DataMember]
        public string Location { get; set; }
    }
}
