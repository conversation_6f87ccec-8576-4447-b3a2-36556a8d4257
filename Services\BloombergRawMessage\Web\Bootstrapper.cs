using Microsoft.Practices.Unity;
using Siepe.Expenses.Biz;
using Siepe.Expenses.Engine;
using Siepe.GenevaUtility;
using Siepe.Instruments.Data;
using Siepe.Instruments.Entities;
using Siepe.Instruments.Entities.Derivatives.Options;
using Siepe.PubSubUtility;
using Siepe.RulesEngine.DataLayer;
using Siepe.RulesEngine.IronPython;
using Siepe.RulesEngine.Services;
using Siepe.Service.BloombergRawMessage.Impl;
using Siepe.Service.BloombergRawMessage.Impl.TradeFeedProcessors.RawTradeProcessor;
using Siepe.Shared.DBUtility;
using Siepe.Shared.WCFUnityUtility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Siepe.Shared.UnityUtility;
using Siepe.IssuerCreation;
using Siepe.Service.BloombergRawMessage.Impl.TradeFeedProcessors;
using Siepe.Serialization.JsonSerializer;
using Siepe.Serialization;

namespace Siepe.Service.BloombergRawMessage.Web
{
    public class Bootstrapper : UnityServiceHostFactory
    {
        protected override void ConfigureContainer(IUnityContainer container)
        {
            container.RegisterType<IGenevaDataProvider, GenevaDataProvider>(new InjectionConstructor(new InjectionParameter("FeedsConnectionString"), new ResolvedParameter<ISqlDbAccess>("Feeds")))
                .RegisterType<IGenevaServiceProvider, GenevaServiceProvider>()
                .RegisterType<ISqlDbAccess, SqlDbAccess>(new InjectionConstructor(new InjectionParameter("CoreConnectionString")))
                .RegisterType<ISqlDbAccess, SqlDbAccess>("Feeds", new InjectionConstructor(new InjectionParameter("FeedsConnectionString")))
                .RegisterType<IRulesRepository, SqlDbAccessDataRepository>(new InjectionConstructor(new ResolvedParameter<ISqlDbAccess>()))
                .RegisterType<IRulesEngine, IronPythonRulesEngine>()
                .RegisterType<IRulesEngineSvc, RulesEngineSvc>(new InjectionConstructor(new ResolvedParameter<IRulesEngine>(), new ResolvedParameter<IRulesRepository>()))
                .RegisterType<ICalculationEngine, CalculationEngine>()
                .RegisterType<IInstrumentProvider, FutureInstrumentProvider>(new InjectionConstructor(new ResolvedParameter<ISqlDbAccess>()))
                .RegisterType<IInstrumentDataProvider, InstrumentDataProvider>(new InjectionConstructor(new ResolvedParameter<ISqlDbAccess>()))
                .RegisterType<IFeeDataProvider, FeeDataProvider>(new InjectionConstructor(new ResolvedParameter<ISqlDbAccess>("Feeds")))
                .RegisterType<IPublicationService, PublicationService>()
                .RegisterType<IIssuerCreator, IssuerCreator>(new InjectionConstructor(new ResolvedParameter<ISqlDbAccess>()))
                .RegisterType<ISerializer, JsonSerializer>()
                ;

            //transaction type
            container.RegisterType<ITransactionResolver, AggregateTransactionResolver>();
            container.ForAssembly(typeof(ITransactionType).Assembly, (a) =>
            {
                a.RegisterInterfaces<ITransactionType>();
            });

            //inst type handlers
            container.RegisterType<IInstTypeResolver, AggregateInstTypeResolver>();
            container.ForAssembly(typeof(IInstrumentTypeHandler).Assembly, (a) =>
            {
                a.RegisterInterfaces<IInstrumentTypeHandler>();
            });


            //instrument type providers
            container.ForAssembly(typeof(IInstrumentProvider).Assembly, (a) =>
            {
                a.RegisterInterfaces<IInstrumentProvider>();
            });

            //handlers
            container
                .RegisterType<ITradeFeedHandler, TradeFeedHandler>()
                .RegisterType<ITradeFeedConfigurationProvider, TradeFeedConfigurationProvider>()
                .RegisterType<IAllocationProvider, AllocationProvider>()
                .RegisterType<IFeeHandler, FeeHandler>(new InjectionConstructor(new ResolvedParameter<ISqlDbAccess>("Feeds"), new ResolvedParameter<IAllocationProvider>(), 
                    new ResolvedParameter<IFeeDataProvider>(), new ResolvedParameter<ICalculationEngine>()));

            //impl
            container.RegisterType<IRawMessageHandler, RawMessageHandler>();

            container.RegisterType<IRawTradeProcessorProvider, RawTradeProcessorProvider>()
                .RegisterType<IRawTradeProcessor, CfdSwapProcessor>(nameof(CfdSwapProcessor), 
                    new ContainerControlledLifetimeManager(), new InjectionConstructor(new ResolvedParameter<ISqlDbAccess>()))
            ;
        }
    }
}