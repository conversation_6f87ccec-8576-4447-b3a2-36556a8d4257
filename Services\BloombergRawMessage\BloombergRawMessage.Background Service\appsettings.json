{"ConnectionStrings": {"CoreConnectionString": "Server=005sql04.highland.aws,52155;Database=HCM;Trusted_Connection=true;", "FeedsConnectionString": "Server=005sql04.highland.aws,52155;Database=DataFeeds;Trusted_Connection=true;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "RabbitMqSettings": {"Host": "b-59ae229a-a617-4d54-b746-0edd8ec85e2b.mq.us-east-1.amazonaws.com", "Port": 5671, "Queue": "queue.bloomberg.raw.messages", "VirtualHost": "Development", "Username": "aristotle-dev", "Password": "<PERSON><PERSON><PERSON>", "UseSsl": false}, "BloombergRawMessageSettings": {"Company": "HoldCo", "ProcessSecMaster": true, "EnableFeeProcessing": false}, "LoggingSettings": {"RabbitMqSettings": {"Enabled": true, "Host": "b-59ae229a-a617-4d54-b746-0edd8ec85e2b.mq.us-east-1.amazonaws.com", "Exchange": "exchange.logging", "VirtualHost": "Development", "Port": 5671, "Username": "aristotle-dev", "Password": "<PERSON><PERSON><PERSON>"}}}