using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using Siepe.Infrastructure.PubSub.Common;
using Siepe.Infrastructure.PubSub.RabbitMq;
using Siepe.Shared.Service.PublishSubscribe.Contract.Dto;

namespace Siepe.Service.BloombergRawMessage.BackgroundWorker
{
    /// <summary>
    /// Bloomberg Raw Message background service that processes trade messages from RabbitMQ.
    /// Excludes Calc.Execution.Fees handling.
    /// </summary>
    public class Worker : Microsoft.Extensions.Hosting.BackgroundService
    {
        private readonly ILogger<Worker> _logger;
        private readonly IPubSubSubscriber _subscriber;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly RabbitMqSettings _rabbitMqSettings;

        public Worker(
            ILogger<Worker> logger,
            IPubSubSubscriber subscriber,
            IServiceScopeFactory serviceScopeFactory,
            IOptions<RabbitMqSettings> rabbitMqSettings)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _subscriber = subscriber ?? throw new ArgumentNullException(nameof(subscriber));
            _serviceScopeFactory = serviceScopeFactory ?? throw new ArgumentNullException(nameof(serviceScopeFactory));
            _rabbitMqSettings = rabbitMqSettings?.Value ?? throw new ArgumentNullException(nameof(rabbitMqSettings));
        }

        public override async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("[BBG RAW Service] Bloomberg Raw Message Service starting...");

            try
            {
                // Subscribe to RabbitMQ messages
                await _subscriber.SubscribeAsync<Message>(_rabbitMqSettings.Queue, OnMessageReceived);
                _logger.LogInformation("[BBG RAW Service] Successfully subscribed to RabbitMQ queue: {Queue}", _rabbitMqSettings.Queue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[BBG RAW Service] Failed to subscribe to RabbitMQ queue: {Queue}", _rabbitMqSettings.Queue);
                throw;
            }

            await base.StartAsync(cancellationToken);
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("[BBG RAW Service] Bloomberg Raw Message Service stopping...");

            try
            {
                // Note: RabbitMQ subscriber cleanup will be handled by disposal
                _logger.LogInformation("[BBG RAW Service] Stopping RabbitMQ subscriber for queue: {Queue}", _rabbitMqSettings.Queue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[BBG RAW Service] Error during RabbitMQ cleanup for queue: {Queue}", _rabbitMqSettings.Queue);
            }

            await base.StopAsync(cancellationToken);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("[BBG RAW Service] Bloomberg Raw Message Service is running and listening for messages...");

            try
            {
                // Keep the service running until cancellation is requested
                while (!stoppingToken.IsCancellationRequested)
                {
                    try
                    {
                        // Service heartbeat - log every 5 minutes to show service is alive
                        await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
                        _logger.LogInformation("[BBG RAW Service] Service heartbeat - running normally and processing Bloomberg trade messages.");
                    }
                    catch (OperationCanceledException)
                    {
                        // Expected when service is stopping
                        break;
                    }
                }

                _logger.LogInformation("[BBG RAW Service] Service main loop completed.");
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                _logger.LogError(ex, "[BBG RAW Service] Error occurred during service execution");
                throw;
            }
        }

        /// <summary>
        /// Handles incoming messages from RabbitMQ
        /// </summary>
        /// <param name="message">The received message</param>
        /// <returns>Task representing the async operation</returns>
        private async Task OnMessageReceived(Message message)
        {
            try
            {
                _logger.LogDebug("[BBG RAW Service] Received message with subject: {Subject}", message?.BaseSubject);

                if (message == null)
                {
                    _logger.LogWarning("[BBG RAW Service] Received null message, skipping processing.");
                    return;
                }

                // Create a scope to resolve scoped services
                using var scope = _serviceScopeFactory.CreateScope();
                var messageProcessor = scope.ServiceProvider.GetRequiredService<MessageProcessor>();

                // Process the message using the MessageProcessor
                await messageProcessor.ProcessMessageAsync(message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[BBG RAW Service] Error processing received message with subject: {Subject}", message?.BaseSubject);

                // Depending on your error handling strategy, you might want to:
                // 1. Dead letter the message
                // 2. Retry with exponential backoff
                // 3. Log and continue (current approach)
                // For now, we log and continue to prevent service from stopping
            }
        }
    }
}
