using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Siepe.Shared.DBUtility.v1;
using Siepe.Instruments.Data;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public class InstrumentProvider : IInstrumentDataProvider
    {
        private readonly IDbAccess _dbAccess;

        public InstrumentProvider(IDbAccess dbAccess)
        {
            _dbAccess = dbAccess ?? throw new ArgumentNullException(nameof(dbAccess));
        }
        public IList<InstrumentInfo> SearchInstrument(string searchText, DateTime? tradeDate)
        {
            
        }

    }
}
